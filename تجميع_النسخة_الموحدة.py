#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تجميع النسخة الموحدة - ريشة عنقاء الأخيرة
تجمع كل المحتوى من الملفات الثلاثة في ملف واحد
"""

import os
from pathlib import Path

def read_file_content(filename):
    """قراءة محتوى ملف"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"❌ لم يتم العثور على الملف: {filename}")
        return ""
    except Exception as e:
        print(f"❌ خطأ في قراءة الملف {filename}: {e}")
        return ""

def create_unified_document():
    """إنشاء المستند الموحد"""
    
    print("🚀 بدء تجميع النسخة الموحدة...")
    print("=" * 50)
    
    # قراءة الملفات الأساسية
    files_to_read = [
        "ريشة_عنقاء_الأخيرة_منسق.md",
        "ريشة_عنقاء_الأخيرة_الجزء_الثاني.md", 
        "ريشة_عنقاء_الأخيرة_الجزء_الثالث.md",
        "صفحة_تعريف_المؤلف_الاحترافية.md"
    ]
    
    content_parts = []
    
    # إضافة الغلاف والمقدمة
    header = """# ❖ ريشة عنقاء الأخيرة - النسخة الموحدة الكاملة ❖

**رواية فانتازيا فلسفية ملحمية - 200 صفحة**

*تأليف: محمد سعيد بوزرهون (عنترة)*  
*شركة: Moxakis MX / Lens Dragon*  
*البريد: <EMAIL> | الهاتف: +212 626 568 075*  
*المدينة الرمزية: أطلانتس – طنجة*

⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺ ⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺

---

## ❖ فهرس المحتويات الكامل ❖

### ⟐ القسم الأول: البدايات ⟐
**الإهداء والمقدمة** ................................. صفحة 3  
**الفصل التمهيدي:** المقدمة الفلسفية .................... صفحة 7  
**الفصل الأول:** يقظة في طنجة ......................... صفحة 15  
**الفصل الثاني:** رحلة بوسيدون عبر البحار .............. صفحة 25  
**الفصل الثالث:** أسرار جزيرة السماء .................. صفحة 35  

### ⟡ القسم الثاني: الأساطير الكبرى ⟡
**الفصل الرابع:** سقوط أطلانتيس ...................... صفحة 45  
**الفصل الخامس:** أفلاطون والأسطورة .................. صفحة 55  
**الفصل السادس:** الحقيقة والخيال ..................... صفحة 65  
**الفصل السابع:** رسالة الإمبراطور عنترة ............... صفحة 75  

### ⟟ القسم الثالث: مملكة القمر العميق ⟟
**الفصل الثامن:** بوابة الضباب ........................ صفحة 85  
**الفصل التاسع:** صفاء، حارسة الأسرار ................. صفحة 95  
**الفصل العاشر:** برج الرمال المتحركة ................. صفحة 105  
**الفصل الحادي عشر:** ظل الطفولة ..................... صفحة 115  
**الفصل الثاني عشر:** لغز النجمة السباعية .............. صفحة 125  
**الفصل الثالث عشر:** الكتاب المتكلم .................. صفحة 135  
**الفصل الرابع عشر:** خيانة في قلب الحرس ............. صفحة 145  
**الفصل الخامس عشر:** تضحية صفاء .................... صفحة 155  
**الفصل السادس عشر:** عودة الإمبراطور ................ صفحة 165  

### ✦ القسم الرابع: الخاتمة والأسرار ✦
**الفصل السابع عشر:** كشف الحقيقة الكاملة ............ صفحة 175  
**الخاتمة الكبرى:** رسالة الإمبراطور الأخيرة ........... صفحة 185  
**عن المؤلف:** عنترة - إمبراطور الكلمات .............. صفحة 195

---

## ⟐ الإهداء والمقدمة ⟐

### ❖ صفحة 3 ❖

**إهداء:**

إلى كل من يؤمن بأن الأساطير ليست مجرد حكايات، بل حقائق لم تُكتشف بعد...  
إلى كل من يحمل في قلبه شعلة الفضول التي لا تنطفئ...  
إلى كل من يرى في المستحيل مجرد كلمة تنتظر من يحولها إلى واقع...

أهدي هذه الرواية.

⟡ ترقيم الصفحة: ❖ 3 ❖ ⟡

---

"""
    
    content_parts.append(header)
    
    # قراءة وإضافة محتوى كل ملف
    for filename in files_to_read:
        print(f"📖 قراءة الملف: {filename}")
        content = read_file_content(filename)
        
        if content:
            # إضافة فاصل بين الملفات
            content_parts.append(f"\n\n---\n\n## 📚 المحتوى من: {filename}\n\n")
            content_parts.append(content)
            print(f"✅ تم قراءة: {filename}")
        else:
            print(f"⚠️ تخطي الملف: {filename}")
    
    # دمج كل المحتوى
    unified_content = "".join(content_parts)
    
    # إضافة الخاتمة
    footer = """

---

## 🎉 النهاية الكاملة 🎉

**❖ ريشة عنقاء الأخيرة - النسخة الموحدة الكاملة ❖**

*"الأسطورة لا تُبنى بالعشوائية، بل بالإرادة والحلم والإصرار على تخليد الحقيقة في قلوب الأجيال."*

**- عنترة، إمبراطور الكلمات والقلوب -**

⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺ ⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺

**تم بحمد الله**

---

## 📊 إحصائيات النسخة الموحدة

- **إجمالي الصفحات:** 200 صفحة
- **عدد الفصول:** 17 فصل + مقدمة وخاتمة
- **عدد الكلمات:** أكثر من 50,000 كلمة
- **النوع الأدبي:** فانتازيا فلسفية ملحمية
- **اللغة:** العربية
- **تاريخ الإكمال:** 2025

---

## 📞 معلومات الاتصال

**المؤلف:** محمد سعيد بوزرهون (عنترة)  
**البريد الإلكتروني:** <EMAIL>  
**الهاتف:** +212 626 568 075  
**الشركة:** Lens Dragon - Moxakis MX  
**المدينة:** طنجة، المغرب

---

**جميع الحقوق محفوظة للمؤلف © 2025**

"""
    
    unified_content += footer
    
    return unified_content

def save_unified_document(content):
    """حفظ المستند الموحد"""
    
    # حفظ كملف Markdown
    md_filename = "ريشة_عنقاء_الأخيرة_النسخة_الكاملة_الموحدة.md"
    try:
        with open(md_filename, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ تم حفظ النسخة الموحدة: {md_filename}")
        
        # إحصائيات
        lines = content.count('\n')
        words = len(content.split())
        chars = len(content)
        
        print(f"📊 إحصائيات الملف:")
        print(f"   📄 عدد الأسطر: {lines:,}")
        print(f"   📝 عدد الكلمات: {words:,}")
        print(f"   🔤 عدد الأحرف: {chars:,}")
        
        return md_filename
        
    except Exception as e:
        print(f"❌ خطأ في حفظ الملف: {e}")
        return None

def create_word_instructions(md_filename):
    """إنشاء تعليمات تحويل Word/PDF"""
    
    instructions = f"""
# 📝 تعليمات تحويل إلى Word/PDF

## 🎯 الملف المصدر:
`{md_filename}`

## 🔧 خطوات التحويل إلى Word:

### الطريقة الأولى - Microsoft Word:
1. افتح Microsoft Word
2. اختر File > Open
3. حدد الملف: `{md_filename}`
4. اختر "Text Files" في نوع الملف
5. طبق التنسيق التالي:
   - **العناوين الرئيسية:** خط 18، غامق، لون أزرق
   - **العناوين الفرعية:** خط 16، غامق، لون بنفسجي
   - **النص العادي:** خط 14، Simplified Arabic
   - **الزخارف:** خط 16، لون ذهبي
6. احفظ كـ: `ريشة_عنقاء_الأخيرة_النسخة_الكاملة.docx`

### الطريقة الثانية - Pandoc (أسرع):
```bash
pandoc "{md_filename}" -o "ريشة_عنقاء_الأخيرة_النسخة_الكاملة.docx"
pandoc "{md_filename}" -o "ريشة_عنقاء_الأخيرة_النسخة_الكاملة.pdf"
```

### الطريقة الثالثة - Online Converter:
1. اذهب إلى: https://pandoc.org/try/
2. ارفع الملف: `{md_filename}`
3. اختر التحويل إلى Word أو PDF
4. حمل النتيجة

## 🎨 تنسيق مقترح للطباعة:

- **حجم الصفحة:** A4
- **الهوامش:** 2.5 سم من كل جهة
- **خط النص:** Simplified Arabic, 14pt
- **خط العناوين:** Simplified Arabic, 18pt, غامق
- **تباعد الأسطر:** 1.5
- **ترقيم الصفحات:** أسفل الصفحة، وسط

## 📚 النتيجة النهائية:
- **ملف Word:** جاهز للتحرير والطباعة
- **ملف PDF:** جاهز للنشر والتوزيع
- **المحتوى:** 200 صفحة كاملة موحدة

🔥 **النسخة الموحدة جاهزة للنشر!** 🔥
"""
    
    instructions_filename = "تعليمات_تحويل_Word_PDF.md"
    with open(instructions_filename, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"📋 تم إنشاء تعليمات التحويل: {instructions_filename}")

def main():
    """الدالة الرئيسية"""
    
    print("🚀 أداة تجميع النسخة الموحدة")
    print("=" * 50)
    
    # إنشاء المستند الموحد
    content = create_unified_document()
    
    if content:
        # حفظ المستند
        md_filename = save_unified_document(content)
        
        if md_filename:
            # إنشاء تعليمات التحويل
            create_word_instructions(md_filename)
            
            print("=" * 50)
            print("🎉 تم الانتهاء بنجاح!")
            print(f"📚 الملف الموحد: {md_filename}")
            print("📝 تعليمات التحويل: تعليمات_تحويل_Word_PDF.md")
            print("🔥 النسخة الموحدة جاهزة للتحويل إلى Word/PDF!")
        else:
            print("❌ فشل في حفظ الملف")
    else:
        print("❌ فشل في إنشاء المحتوى")

if __name__ == "__main__":
    main()
