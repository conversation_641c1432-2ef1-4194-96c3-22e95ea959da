# 📚 تعليمات إكمال ملف EPUB - ريشة عنقاء الأخيرة

## 🎯 **الوضع الحالي:**
تم إنشاء الهيكل الأساسي لملف EPUB مع:
- ✅ **META-INF/container.xml** - ملف التكوين الأساسي
- ✅ **OEBPS/content.opf** - فهرس الكتاب والبيانات الوصفية
- ✅ **OEBPS/styles/book.css** - أنماط التصميم الاحترافية
- ✅ **OEBPS/cover.xhtml** - صفحة الغلاف
- ✅ **OEBPS/title.xhtml** - صفحة العنوان
- ✅ **OEBPS/dedication.xhtml** - صفحة الإهداء
- ✅ **OEBPS/toc.xhtml** - فهرس المحتويات
- ✅ **OEBPS/intro.xhtml** - الفصل التمهيدي (مثال)
- ✅ **OEBPS/about-author.xhtml** - صفحة المؤلف

---

## 📋 **المطلوب لإكمال EPUB:**

### 1️⃣ **إنشاء ملفات الفصول المتبقية:**

يجب إنشاء الملفات التالية في مجلد `OEBPS/`:

```
chapter01.xhtml - الفصل الأول: يقظة في طنجة
chapter02.xhtml - الفصل الثاني: رحلة بوسيدون عبر البحار
chapter03.xhtml - الفصل الثالث: أسرار جزيرة السماء
chapter04.xhtml - الفصل الرابع: سقوط أطلانتيس
chapter05.xhtml - الفصل الخامس: أفلاطون والأسطورة
chapter06.xhtml - الفصل السادس: الحقيقة والخيال
chapter07.xhtml - الفصل السابع: رسالة الإمبراطور عنترة
chapter08.xhtml - الفصل الثامن: بوابة الضباب
chapter09.xhtml - الفصل التاسع: صفاء، حارسة الأسرار
chapter10.xhtml - الفصل العاشر: برج الرمال المتحركة
chapter11.xhtml - الفصل الحادي عشر: ظل الطفولة
chapter12.xhtml - الفصل الثاني عشر: لغز النجمة السباعية
chapter13.xhtml - الفصل الثالث عشر: الكتاب المتكلم
chapter14.xhtml - الفصل الرابع عشر: خيانة في قلب الحرس
chapter15.xhtml - الفصل الخامس عشر: تضحية صفاء
chapter16.xhtml - الفصل السادس عشر: عودة الإمبراطور
chapter17.xhtml - الفصل السابع عشر: كشف الحقيقة الكاملة
epilogue.xhtml - الخاتمة الكبرى
```

### 2️⃣ **قالب لإنشاء كل فصل:**

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="ar" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8"/>
    <title>[عنوان الفصل]</title>
    <link rel="stylesheet" type="text/css" href="styles/book.css"/>
</head>
<body>
    <div class="chapter">
        <div class="decoration">⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺</div>
        
        <h1 class="chapter-title">[عنوان الفصل]</h1>
        
        <div class="decoration">⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺</div>
        
        <!-- محتوى الفصل هنا -->
        
        <div class="decoration">⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺</div>
    </div>
</body>
</html>
```

### 3️⃣ **إنشاء مجلد الصور:**

إنشاء مجلد `OEBPS/images/` ووضع الصور التالية:
- `cover.jpg` - صورة الغلاف
- `antar1.jpg` - صورة المؤلف

### 4️⃣ **إنشاء ملف التنقل:**

إنشاء `OEBPS/nav.xhtml`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops" xml:lang="ar" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8"/>
    <title>التنقل</title>
    <link rel="stylesheet" type="text/css" href="styles/book.css"/>
</head>
<body>
    <nav epub:type="toc">
        <h1>فهرس المحتويات</h1>
        <ol>
            <li><a href="cover.xhtml">الغلاف</a></li>
            <li><a href="title.xhtml">صفحة العنوان</a></li>
            <li><a href="dedication.xhtml">الإهداء</a></li>
            <li><a href="toc.xhtml">فهرس المحتويات</a></li>
            <li><a href="intro.xhtml">الفصل التمهيدي</a></li>
            <!-- باقي الفصول -->
            <li><a href="about-author.xhtml">عن المؤلف</a></li>
        </ol>
    </nav>
</body>
</html>
```

### 5️⃣ **إنشاء ملف NCX:**

إنشاء `OEBPS/toc.ncx`:

```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE ncx PUBLIC "-//NISO//DTD ncx 2005-1//EN" "http://www.daisy.org/z3986/2005/ncx-2005-1.dtd">
<ncx xmlns="http://www.daisy.org/z3986/2005/ncx/" version="2005-1">
    <head>
        <meta name="dtb:uid" content="urn:uuid:12345678-1234-1234-1234-123456789012"/>
        <meta name="dtb:depth" content="2"/>
        <meta name="dtb:totalPageCount" content="0"/>
        <meta name="dtb:maxPageNumber" content="0"/>
    </head>
    <docTitle>
        <text>ريشة عنقاء الأخيرة</text>
    </docTitle>
    <navMap>
        <navPoint id="cover" playOrder="1">
            <navLabel><text>الغلاف</text></navLabel>
            <content src="cover.xhtml"/>
        </navPoint>
        <!-- باقي العناصر -->
    </navMap>
</ncx>
```

---

## 🛠️ **خطوات إنشاء EPUB النهائي:**

### الطريقة الأولى: يدوياً

1. **إنشاء مجلد جديد** باسم `ريشة_عنقاء_الأخيرة_epub`
2. **نسخ جميع الملفات** المنشأة إلى المجلد
3. **إكمال الفصول المتبقية** باستخدام القالب
4. **ضغط المجلد** إلى ملف ZIP
5. **تغيير امتداد الملف** من `.zip` إلى `.epub`

### الطريقة الثانية: باستخدام أدوات EPUB

1. **تحميل Calibre** (مجاني)
2. **استيراد الملفات** إلى Calibre
3. **تحويل إلى EPUB** باستخدام الأدوات المدمجة

### الطريقة الثالثة: باستخدام Pandoc

```bash
pandoc -o ريشة_عنقاء_الأخيرة.epub --epub-metadata=metadata.xml *.xhtml
```

---

## 📝 **نصائح مهمة:**

### ✅ **للحصول على أفضل النتائج:**

1. **تأكد من ترميز UTF-8** لجميع الملفات
2. **استخدم الزخارف العربية** المحددة في CSS
3. **اتبع ترقيم الصفحات** كما هو محدد
4. **أضف وصف الصور** في كل صفحة زوجية
5. **حافظ على التنسيق** المحدد في القالب

### 🎨 **للتصميم:**

1. **الألوان محددة** في ملف CSS
2. **الخطوط العربية** مدمجة
3. **التخطيط متجاوب** للشاشات المختلفة
4. **الزخارف متناسقة** مع الطابع العربي

### 📱 **للتوافق:**

1. **اختبر على قارئات مختلفة** (Kindle, iBooks, Adobe)
2. **تأكد من اتجاه النص** (من اليمين لليسار)
3. **فحص الروابط الداخلية**
4. **التأكد من عرض الصور**

---

## 🚀 **الخطوة التالية:**

### 📋 **قائمة المهام:**

- [ ] إنشاء جميع ملفات الفصول (17 فصل)
- [ ] إضافة الصور المطلوبة
- [ ] إنشاء ملف التنقل
- [ ] إنشاء ملف NCX
- [ ] اختبار EPUB على قارئات مختلفة
- [ ] تصدير النسخة النهائية

### 🎯 **النتيجة المتوقعة:**

ملف **`ريشة_عنقاء_الأخيرة.epub`** كامل ومتوافق مع جميع قارئات الكتب الإلكترونية، يحتوي على:

- ✅ **200 صفحة** من المحتوى
- ✅ **تصميم احترافي** بالزخارف العربية
- ✅ **تنقل سهل** بين الفصول
- ✅ **صور وأوصاف** تعبيرية
- ✅ **معلومات المؤلف** كاملة
- ✅ **فهرس شامل** ومنظم

---

**🔥 ابدأ الآن في إنشاء الفصول واحداً تلو الآخر! 🔥**
