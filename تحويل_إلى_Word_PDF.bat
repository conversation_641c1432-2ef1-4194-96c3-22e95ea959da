@echo off
echo ========================================
echo تحويل ريشة عنقاء الأخيرة إلى Word/PDF
echo ========================================
echo.

REM التحقق من وجود pandoc
where pandoc >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Pandoc غير مثبت
    echo 📥 يرجى تحميله من: https://pandoc.org/installing.html
    echo.
    echo 🔧 أو استخدم الطريقة اليدوية:
    echo 1. افتح Microsoft Word
    echo 2. اختر File ^> Open
    echo 3. حدد الملف: ريشة_عنقاء_الأخيرة_النسخة_الموحدة.docx.md
    echo 4. احفظ كـ Word أو PDF
    pause
    exit /b 1
)

echo ✅ تم العثور على Pandoc
echo.

REM التحقق من وجود الملف المصدر
if not exist "ريشة_عنقاء_الأخيرة_النسخة_الموحدة.docx.md" (
    echo ❌ لم يتم العثور على الملف المصدر
    echo 📁 تأكد من وجود: ريشة_عنقاء_الأخيرة_النسخة_الموحدة.docx.md
    pause
    exit /b 1
)

echo 📚 تحويل إلى Word...
pandoc "ريشة_عنقاء_الأخيرة_النسخة_الموحدة.docx.md" -o "ريشة_عنقاء_الأخيرة_النسخة_الكاملة.docx" --from markdown --to docx

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء ملف Word بنجاح
) else (
    echo ❌ فشل في إنشاء ملف Word
)

echo.
echo 📄 تحويل إلى PDF...
pandoc "ريشة_عنقاء_الأخيرة_النسخة_الموحدة.docx.md" -o "ريشة_عنقاء_الأخيرة_النسخة_الكاملة.pdf" --from markdown --to pdf --pdf-engine=xelatex

if %errorlevel% equ 0 (
    echo ✅ تم إنشاء ملف PDF بنجاح
) else (
    echo ❌ فشل في إنشاء ملف PDF
    echo 💡 تلميح: قد تحتاج إلى تثبيت LaTeX
)

echo.
echo ========================================
echo 🎉 انتهت عملية التحويل
echo ========================================
echo.

REM عرض الملفات المنشأة
if exist "ريشة_عنقاء_الأخيرة_النسخة_الكاملة.docx" (
    echo 📄 ملف Word: ريشة_عنقاء_الأخيرة_النسخة_الكاملة.docx
)

if exist "ريشة_عنقاء_الأخيرة_النسخة_الكاملة.pdf" (
    echo 📄 ملف PDF: ريشة_عنقاء_الأخيرة_النسخة_الكاملة.pdf
)

echo.
echo 🚀 الملفات جاهزة للنشر!
echo.
pause
