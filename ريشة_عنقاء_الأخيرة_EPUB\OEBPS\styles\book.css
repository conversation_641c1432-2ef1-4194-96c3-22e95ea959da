/* ريشة عنقاء الأخيرة - أنماط الكتاب الإلكتروني */

@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Scheherazade+New:wght@400;700&display=swap');

/* الإعدادات العامة */
body {
    font-family: '<PERSON><PERSON>', 'Scheherazade New', serif;
    font-size: 1.1em;
    line-height: 1.8;
    margin: 0;
    padding: 20px;
    text-align: right;
    direction: rtl;
    background-color: #fefefe;
    color: #2c3e50;
}

/* العناوين */
h1 {
    font-family: 'Scheherazade New', serif;
    font-size: 2.5em;
    font-weight: 700;
    text-align: center;
    color: #c0392b;
    margin: 40px 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

h2 {
    font-family: 'Scheherazade New', serif;
    font-size: 2em;
    font-weight: 700;
    color: #8e44ad;
    margin: 30px 0 20px 0;
    text-align: center;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
}

h3 {
    font-family: 'Scheherazade New', serif;
    font-size: 1.5em;
    font-weight: 700;
    color: #2980b9;
    margin: 25px 0 15px 0;
    text-align: center;
}

h4 {
    font-size: 1.3em;
    font-weight: 700;
    color: #27ae60;
    margin: 20px 0 10px 0;
}

/* الفقرات */
p {
    margin: 15px 0;
    text-indent: 30px;
    text-align: justify;
}

/* الزخارف العربية */
.decoration {
    text-align: center;
    font-size: 1.5em;
    color: #f39c12;
    margin: 20px 0;
    letter-spacing: 5px;
}

/* ترقيم الصفحات */
.page-number {
    text-align: center;
    font-size: 1.2em;
    color: #7f8c8d;
    margin: 30px 0;
    font-weight: bold;
    border: 2px solid #ecf0f1;
    padding: 10px;
    border-radius: 10px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
}

/* الاقتباسات */
blockquote {
    font-style: italic;
    font-size: 1.2em;
    color: #8e44ad;
    border-right: 4px solid #9b59b6;
    padding-right: 20px;
    margin: 25px 0;
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
}

/* النصوص المميزة */
.highlight {
    background-color: #fff3cd;
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: bold;
}

/* الحوارات */
.dialogue {
    font-style: italic;
    color: #2c3e50;
    margin: 10px 20px;
    padding: 10px;
    border-right: 3px solid #3498db;
    background-color: #ecf0f1;
}

/* وصف الصور */
.image-description {
    background-color: #e8f5e8;
    border: 2px dashed #27ae60;
    padding: 20px;
    margin: 20px 0;
    border-radius: 10px;
    text-align: center;
    font-style: italic;
    color: #27ae60;
}

.image-description h4 {
    color: #27ae60;
    margin-top: 0;
}

/* صفحة الغلاف */
.cover-page {
    text-align: center;
    padding: 50px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    min-height: 80vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.cover-title {
    font-size: 3em;
    font-weight: bold;
    margin: 20px 0;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
}

.cover-subtitle {
    font-size: 1.5em;
    margin: 10px 0;
    opacity: 0.9;
}

.cover-author {
    font-size: 1.8em;
    margin: 30px 0;
    font-weight: bold;
}

/* صفحة المؤلف */
.author-page {
    text-align: center;
    padding: 30px;
}

.author-image {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    margin: 20px auto;
    border: 5px solid #3498db;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* فهرس المحتويات */
.toc {
    padding: 20px;
}

.toc ul {
    list-style: none;
    padding: 0;
}

.toc li {
    margin: 10px 0;
    padding: 10px;
    border-bottom: 1px dotted #bdc3c7;
}

.toc a {
    text-decoration: none;
    color: #2c3e50;
    font-weight: bold;
}

.toc a:hover {
    color: #3498db;
}

/* الفصول */
.chapter {
    page-break-before: always;
    margin-top: 50px;
}

.chapter-title {
    text-align: center;
    font-size: 2.5em;
    color: #c0392b;
    margin: 40px 0;
    border-bottom: 3px solid #ecf0f1;
    padding-bottom: 20px;
}

/* النهاية */
.epilogue {
    text-align: center;
    font-size: 1.3em;
    color: #8e44ad;
    font-style: italic;
    margin: 50px 0;
    padding: 30px;
    background: linear-gradient(45deg, #f8f9fa, #e9ecef);
    border-radius: 15px;
}

/* الروابط */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    color: #2980b9;
    text-decoration: underline;
}

/* الجداول */
table {
    width: 100%;
    border-collapse: collapse;
    margin: 20px 0;
}

th, td {
    border: 1px solid #bdc3c7;
    padding: 12px;
    text-align: center;
}

th {
    background-color: #3498db;
    color: white;
    font-weight: bold;
}

/* القوائم */
ul, ol {
    margin: 15px 0;
    padding-right: 30px;
}

li {
    margin: 8px 0;
}

/* الخطوط المميزة */
.bold {
    font-weight: bold;
}

.italic {
    font-style: italic;
}

.underline {
    text-decoration: underline;
}

/* الألوان المميزة */
.red {
    color: #e74c3c;
}

.blue {
    color: #3498db;
}

.green {
    color: #27ae60;
}

.purple {
    color: #9b59b6;
}

.gold {
    color: #f39c12;
}

/* تأثيرات خاصة */
.glow {
    text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
}

.shadow {
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* استجابة للشاشات الصغيرة */
@media screen and (max-width: 600px) {
    body {
        font-size: 1em;
        padding: 10px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    h2 {
        font-size: 1.5em;
    }
    
    .cover-title {
        font-size: 2em;
    }
    
    .author-image {
        width: 150px;
        height: 150px;
    }
}

/* طباعة */
@media print {
    body {
        font-size: 12pt;
        line-height: 1.6;
    }
    
    .page-number {
        page-break-after: always;
    }
    
    .chapter {
        page-break-before: always;
    }
}
