#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة إنشاء EPUB سريع - ريشة عنقاء الأخيرة
تجمع كل المحتوى في ملف EPUB واحد
"""

import os
import zipfile
import shutil
from pathlib import Path

def create_epub_structure():
    """إنشاء هيكل EPUB"""
    
    # إنشاء المجلدات
    epub_dir = Path("ريشة_عنقاء_الأخيرة_EPUB")
    if epub_dir.exists():
        shutil.rmtree(epub_dir)
    
    epub_dir.mkdir()
    (epub_dir / "META-INF").mkdir()
    (epub_dir / "OEBPS").mkdir()
    (epub_dir / "OEBPS" / "styles").mkdir()
    (epub_dir / "OEBPS" / "images").mkdir()
    
    return epub_dir

def copy_existing_files(epub_dir):
    """نسخ الملفات الموجودة"""
    
    files_to_copy = [
        ("META-INF/container.xml", "META-INF/container.xml"),
        ("OEBPS/content.opf", "OEBPS/content.opf"),
        ("OEBPS/styles/book.css", "OEBPS/styles/book.css"),
        ("OEBPS/cover.xhtml", "OEBPS/cover.xhtml"),
        ("OEBPS/title.xhtml", "OEBPS/title.xhtml"),
        ("OEBPS/dedication.xhtml", "OEBPS/dedication.xhtml"),
        ("OEBPS/toc.xhtml", "OEBPS/toc.xhtml"),
        ("OEBPS/intro.xhtml", "OEBPS/intro.xhtml"),
        ("OEBPS/about-author.xhtml", "OEBPS/about-author.xhtml"),
    ]
    
    for src, dst in files_to_copy:
        src_path = Path(src)
        dst_path = epub_dir / dst
        
        if src_path.exists():
            shutil.copy2(src_path, dst_path)
            print(f"✅ تم نسخ: {src}")
        else:
            print(f"❌ لم يتم العثور على: {src}")

def create_missing_chapters(epub_dir):
    """إنشاء الفصول المفقودة"""
    
    # قالب الفصل
    chapter_template = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="ar" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8"/>
    <title>{title}</title>
    <link rel="stylesheet" type="text/css" href="styles/book.css"/>
</head>
<body>
    <div class="chapter">
        <div class="decoration">⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺</div>
        
        <h1 class="chapter-title">{title}</h1>
        
        <div class="decoration">⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺</div>
        
        <p><strong>هذا الفصل يحتاج إلى المحتوى من الملفات الأصلية.</strong></p>
        <p>يرجى نسخ المحتوى من:</p>
        <ul>
            <li>ريشة_عنقاء_الأخيرة_الرواية_الكاملة.md</li>
            <li>ريشة_عنقاء_الأخيرة_الجزء_الثاني.md</li>
            <li>ريشة_عنقاء_الأخيرة_الجزء_الثالث.md</li>
        </ul>
        
        <div class="page-number">⟡ ترقيم الصفحة: ❖ {page} ❖ ⟡</div>
        
        <div class="decoration">⟐ ⟡ ⟟ ✦ ✵ ⬖ ⬗ ⬘ ⬙ ✺</div>
    </div>
</body>
</html>'''

    chapters = [
        ("chapter01.xhtml", "الفصل الأول: يقظة في طنجة", 15),
        ("chapter02.xhtml", "الفصل الثاني: رحلة بوسيدون عبر البحار", 25),
        ("chapter03.xhtml", "الفصل الثالث: أسرار جزيرة السماء", 35),
        ("chapter04.xhtml", "الفصل الرابع: سقوط أطلانتيس", 45),
        ("chapter05.xhtml", "الفصل الخامس: أفلاطون والأسطورة", 55),
        ("chapter06.xhtml", "الفصل السادس: الحقيقة والخيال", 65),
        ("chapter07.xhtml", "الفصل السابع: رسالة الإمبراطور عنترة", 75),
        ("chapter08.xhtml", "الفصل الثامن: بوابة الضباب", 85),
        ("chapter09.xhtml", "الفصل التاسع: صفاء، حارسة الأسرار", 95),
        ("chapter10.xhtml", "الفصل العاشر: برج الرمال المتحركة", 105),
        ("chapter11.xhtml", "الفصل الحادي عشر: ظل الطفولة", 115),
        ("chapter12.xhtml", "الفصل الثاني عشر: لغز النجمة السباعية", 125),
        ("chapter13.xhtml", "الفصل الثالث عشر: الكتاب المتكلم", 135),
        ("chapter14.xhtml", "الفصل الرابع عشر: خيانة في قلب الحرس", 145),
        ("chapter15.xhtml", "الفصل الخامس عشر: تضحية صفاء", 155),
        ("chapter16.xhtml", "الفصل السادس عشر: عودة الإمبراطور", 165),
        ("chapter17.xhtml", "الفصل السابع عشر: كشف الحقيقة الكاملة", 175),
        ("epilogue.xhtml", "الخاتمة الكبرى: رسالة الإمبراطور الأخيرة", 185),
    ]
    
    for filename, title, page in chapters:
        chapter_content = chapter_template.format(title=title, page=page)
        chapter_path = epub_dir / "OEBPS" / filename
        
        with open(chapter_path, 'w', encoding='utf-8') as f:
            f.write(chapter_content)
        
        print(f"✅ تم إنشاء: {filename}")

def create_nav_file(epub_dir):
    """إنشاء ملف التنقل"""
    
    nav_content = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:epub="http://www.idpf.org/2007/ops" xml:lang="ar" lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8"/>
    <title>التنقل</title>
    <link rel="stylesheet" type="text/css" href="styles/book.css"/>
</head>
<body>
    <nav epub:type="toc">
        <h1>فهرس المحتويات</h1>
        <ol>
            <li><a href="cover.xhtml">الغلاف</a></li>
            <li><a href="title.xhtml">صفحة العنوان</a></li>
            <li><a href="dedication.xhtml">الإهداء</a></li>
            <li><a href="toc.xhtml">فهرس المحتويات</a></li>
            <li><a href="intro.xhtml">الفصل التمهيدي</a></li>
            <li><a href="chapter01.xhtml">الفصل الأول</a></li>
            <li><a href="chapter02.xhtml">الفصل الثاني</a></li>
            <li><a href="chapter03.xhtml">الفصل الثالث</a></li>
            <li><a href="chapter04.xhtml">الفصل الرابع</a></li>
            <li><a href="chapter05.xhtml">الفصل الخامس</a></li>
            <li><a href="chapter06.xhtml">الفصل السادس</a></li>
            <li><a href="chapter07.xhtml">الفصل السابع</a></li>
            <li><a href="chapter08.xhtml">الفصل الثامن</a></li>
            <li><a href="chapter09.xhtml">الفصل التاسع</a></li>
            <li><a href="chapter10.xhtml">الفصل العاشر</a></li>
            <li><a href="chapter11.xhtml">الفصل الحادي عشر</a></li>
            <li><a href="chapter12.xhtml">الفصل الثاني عشر</a></li>
            <li><a href="chapter13.xhtml">الفصل الثالث عشر</a></li>
            <li><a href="chapter14.xhtml">الفصل الرابع عشر</a></li>
            <li><a href="chapter15.xhtml">الفصل الخامس عشر</a></li>
            <li><a href="chapter16.xhtml">الفصل السادس عشر</a></li>
            <li><a href="chapter17.xhtml">الفصل السابع عشر</a></li>
            <li><a href="epilogue.xhtml">الخاتمة</a></li>
            <li><a href="about-author.xhtml">عن المؤلف</a></li>
        </ol>
    </nav>
</body>
</html>'''
    
    nav_path = epub_dir / "OEBPS" / "nav.xhtml"
    with open(nav_path, 'w', encoding='utf-8') as f:
        f.write(nav_content)
    
    print("✅ تم إنشاء ملف التنقل")

def create_ncx_file(epub_dir):
    """إنشاء ملف NCX"""
    
    ncx_content = '''<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE ncx PUBLIC "-//NISO//DTD ncx 2005-1//EN" "http://www.daisy.org/z3986/2005/ncx-2005-1.dtd">
<ncx xmlns="http://www.daisy.org/z3986/2005/ncx/" version="2005-1">
    <head>
        <meta name="dtb:uid" content="urn:uuid:12345678-1234-1234-1234-123456789012"/>
        <meta name="dtb:depth" content="2"/>
        <meta name="dtb:totalPageCount" content="0"/>
        <meta name="dtb:maxPageNumber" content="0"/>
    </head>
    <docTitle>
        <text>ريشة عنقاء الأخيرة</text>
    </docTitle>
    <navMap>
        <navPoint id="cover" playOrder="1">
            <navLabel><text>الغلاف</text></navLabel>
            <content src="cover.xhtml"/>
        </navPoint>
        <navPoint id="title" playOrder="2">
            <navLabel><text>صفحة العنوان</text></navLabel>
            <content src="title.xhtml"/>
        </navPoint>
        <navPoint id="dedication" playOrder="3">
            <navLabel><text>الإهداء</text></navLabel>
            <content src="dedication.xhtml"/>
        </navPoint>
        <navPoint id="toc" playOrder="4">
            <navLabel><text>فهرس المحتويات</text></navLabel>
            <content src="toc.xhtml"/>
        </navPoint>
        <navPoint id="intro" playOrder="5">
            <navLabel><text>الفصل التمهيدي</text></navLabel>
            <content src="intro.xhtml"/>
        </navPoint>
        <navPoint id="about" playOrder="25">
            <navLabel><text>عن المؤلف</text></navLabel>
            <content src="about-author.xhtml"/>
        </navPoint>
    </navMap>
</ncx>'''
    
    ncx_path = epub_dir / "OEBPS" / "toc.ncx"
    with open(ncx_path, 'w', encoding='utf-8') as f:
        f.write(ncx_content)
    
    print("✅ تم إنشاء ملف NCX")

def copy_images(epub_dir):
    """نسخ الصور"""
    
    # نسخ صورة المؤلف إذا كانت موجودة
    if Path("antar1.jpg").exists():
        shutil.copy2("antar1.jpg", epub_dir / "OEBPS" / "images" / "antar1.jpg")
        print("✅ تم نسخ صورة المؤلف")
    else:
        print("⚠️ لم يتم العثور على صورة المؤلف (antar1.jpg)")

def create_epub_file(epub_dir):
    """إنشاء ملف EPUB النهائي"""
    
    epub_filename = "ريشة_عنقاء_الأخيرة.epub"
    
    with zipfile.ZipFile(epub_filename, 'w', zipfile.ZIP_DEFLATED) as epub_zip:
        # إضافة mimetype أولاً (بدون ضغط)
        epub_zip.writestr("mimetype", "application/epub+zip", compress_type=zipfile.ZIP_STORED)
        
        # إضافة باقي الملفات
        for root, dirs, files in os.walk(epub_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(epub_dir)
                epub_zip.write(file_path, arc_path)
    
    print(f"🎉 تم إنشاء ملف EPUB: {epub_filename}")
    return epub_filename

def main():
    """الدالة الرئيسية"""
    
    print("🚀 بدء إنشاء ملف EPUB...")
    print("=" * 50)
    
    # إنشاء هيكل EPUB
    epub_dir = create_epub_structure()
    print("✅ تم إنشاء هيكل EPUB")
    
    # نسخ الملفات الموجودة
    copy_existing_files(epub_dir)
    
    # إنشاء الفصول المفقودة
    create_missing_chapters(epub_dir)
    
    # إنشاء ملفات التنقل
    create_nav_file(epub_dir)
    create_ncx_file(epub_dir)
    
    # نسخ الصور
    copy_images(epub_dir)
    
    # إنشاء ملف EPUB النهائي
    epub_file = create_epub_file(epub_dir)
    
    print("=" * 50)
    print("🎉 تم الانتهاء بنجاح!")
    print(f"📚 ملف EPUB جاهز: {epub_file}")
    print("📝 ملاحظة: يجب إضافة المحتوى الفعلي للفصول من الملفات الأصلية")
    print("🔧 استخدم محرر EPUB مثل Calibre لإضافة المحتوى")

if __name__ == "__main__":
    main()
